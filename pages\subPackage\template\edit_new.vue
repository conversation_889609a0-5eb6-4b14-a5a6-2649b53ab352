<template>
	<view class="edit-container">
		<!-- Canvas画布区域 -->
		<view class="canvas-section">
			<view class="canvas-header">
				<text class="canvas-title">{{ templateName || '未命名模板' }}</text>
				<view class="canvas-info">
					<text class="canvas-size">{{ canvasWidth }}×{{ canvasHeight }}</text>
					<text class="element-count">{{ canvasElements.length }}个元素</text>
				</view>
			</view>
			<CanvasEditor
				ref="canvasEditor"
				:canvas-width="canvasWidth"
				:canvas-height="canvasHeight"
				:canvas-elements="canvasElements"
				:selected-element="selectedElement"
				:background-image="backgroundImage"
				:background-color="backgroundColor"
				@element-selected="onElementSelected"
				@element-updated="onElementUpdated"
				@element-deleted="onElementDeleted"
				@drag-start="onDragStart"
				@drag-move="onDragMove"
				@drag-end="onDragEnd"
				@element-long-press="onElementLongPress"
				@element-copy="onElementCopy"
				@error="onCanvasError"
			/>
			<!-- 操作提示 -->
			<view v-if="showOperationTip" class="operation-tip" :class="operationTipType">
				<text class="tip-text">{{ operationTipText }}</text>
			</view>
		</view>

		<!-- 分隔线 -->
		<view class="divider"></view>

		<!-- 属性设置界面区域 -->
		<scroll-view scroll-y class="property-section">
			<!-- 模版基本信息 -->
			<TemplateBasicInfo
				:template-name="templateName"
				:template-type-index="templateTypeIndex"
				:template-type-list="templateTypeList"
				@update:template-name="templateName = $event"
				@update:template-type-index="templateTypeIndex = $event"
				@template-type-change="onTemplateTypeChange"
			/>

			<!-- 控制按钮区域 -->
			<ControlButtons
				@insert-template-text="insertTemplateText"
				@insert-fixed-text="insertFixedText"
				@insert-image="insertImage"
				@select-background="selectBackground"
				@set-solid-background="setSolidBackground"
				@clear-background="clearBackground"
				@center-all-elements="centerAllElements"
				@auto-adjust-spacing="autoAdjustSpacing"
				@beautify-all-elements="beautifyAllElements"
			/>
			<!-- 属性设置面板 -->
			<PropertyPanel
				:selected-element="selectedElement"
				:font-family-index="fontFamilyIndex"
				:font-family-list="fontFamilyList"
				:text-color="textColor"
				:color-name-map="colorNameMap"
				:is-bold="isBold"
				:is-italic="isItalic"
				:is-underline="isUnderline"
				:is-strikethrough="isStrikethrough"
				:height-scale="heightScale"
				:font-size="fontSize"
				@font-family-change="onFontFamilyChange"
				@text-color-picker="openTextColorPicker"
				@toggle-bold="toggleBold"
				@toggle-italic="toggleItalic"
				@toggle-underline="toggleUnderline"
				@toggle-strikethrough="toggleStrikethrough"
				@height-scale-change="onHeightScaleChange"
				@height-scale-increase="increaseHeightScale"
				@height-scale-decrease="decreaseHeightScale"
				@font-size-change="onFontSizeChange"
				@font-size-increase="increaseFontSize"
				@font-size-decrease="decreaseFontSize"
			/>
			
			<!-- 保存按钮 -->
			<view class="action-buttons">
				<view class="preview-button" @click="previewTemplate">预 览</view>
				<view class="save-button" @click="saveTemplate">保 存</view>
			</view>
			
		</scroll-view>
		
		<!-- 颜色选择器弹窗 -->
		<view v-if="showColorPicker" class="color-picker-modal" @click="closeColorPicker">
			<view class="color-picker-content" @click.stop="stopPropagation">
				<view class="color-picker-title">
					<text v-if="isTextColorPicker">{{templateTypeList[templateTypeIndex]}}颜色选择</text>
					<text v-else-if="isBackgroundColorPicker">{{templateTypeList[templateTypeIndex]}}背景颜色</text>
				</view>
				<view class="color-grid">
					<view
						v-for="color in currentColorList"
						:key="color"
						class="color-item-container"
					>
						<view 
							class="color-item" 
							:style="{backgroundColor: color}"
							@click="selectColor(color)"
						></view>
						<text class="color-item-name">{{colorNameMap[color] }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 模板文字弹窗 -->
		<u-popup :show="showTemplateTextPopup" mode="bottom" :closeable="true" @close="onTemplatePopupClose" :round="16" :safeAreaInsetBottom="true">
			<view class="template-popup-content">
				<view class="popup-title">添加模版文字</view>

				<view class="template-field">
					<text class="field-label">姓名</text>
					<input v-model="templateFields.name" class="field-input" placeholder="请输入姓名" />
				</view>

				<view class="template-field">
					<text class="field-label">职位</text>
					<input v-model="templateFields.position" class="field-input" placeholder="请输入职位" />
				</view>

				<view class="template-field">
					<text class="field-label">公司</text>
					<input v-model="templateFields.company" class="field-input" placeholder="请输入公司" />
				</view>

				<view class="template-field">
					<text class="field-label">其他信息</text>
					<input v-model="templateFields.other" class="field-input" placeholder="请输入其他信息" />
				</view>

				<view class="popup-btns">
					<view class="popup-btn cancel" @click="onTemplatePopupClose">取消</view>
					<view class="popup-btn confirm" @click="confirmAddTemplateText">确认</view>
				</view>
			</view>
		</u-popup>

		<!-- 预览弹窗 -->
		<u-popup :show="showPreviewPopup" mode="center" :closeable="true" @close="closePreview" :round="16" :safeAreaInsetBottom="true">
			<view class="preview-popup-content">
				<view class="popup-title">模板预览</view>

				<view class="preview-image-container">
					<image v-if="previewImagePath" :src="previewImagePath" mode="aspectFit" class="preview-image"></image>
					<view v-else-if="previewLoading" class="preview-loading">
						<text>生成预览中...</text>
					</view>
					<view v-else class="preview-error">
						<text>预览生成失败</text>
					</view>
				</view>

				<view class="popup-btns">
					<view class="popup-btn cancel" @click="closePreview">关闭</view>
					<view class="popup-btn confirm" @click="savePreviewImage" v-if="previewImagePath">保存图片</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import CanvasEditor from '@/components/CanvasEditor/CanvasEditor.vue'
	import TemplateBasicInfo from '@/components/TemplateEditor/TemplateBasicInfo.vue'
	import ControlButtons from '@/components/TemplateEditor/ControlButtons.vue'
	import PropertyPanel from '@/components/TemplateEditor/PropertyPanel.vue'

	// 导入常量和工具函数
	import {
		TEMPLATE_TYPES,
		COLOR_NAMES,
		FONT_FAMILIES,
		DEFAULT_TEMPLATE_FIELDS,
		CANVAS_CONFIG,
		FONT_SIZE_LIMITS,
		HEIGHT_SCALE_LIMITS,
		ELEMENT_TYPES,
		ERROR_TYPES,
		MESSAGES
	} from '@/constants/templateEditor.js'

	import {
		validateTemplateName,
		validateColor,
		validateFontSize,
		validateHeightScale,
		generateUniqueId,
		deepClone,
		getDeviceInfo,
		calculateCanvasSize,
		formatError,
		safeExecute
	} from '@/utils/templateEditor.js'

	/**
	 * 模板编辑器主页面
	 * 电子桌牌模板编辑功能
	 *
	 * <AUTHOR> Assistant
	 * @version 2.0.0
	 * @since 2024-06-24
	 */
	export default {
		name: 'TemplateEditNew',
		components: {
			CanvasEditor,
			TemplateBasicInfo,
			ControlButtons,
			PropertyPanel
		},
		onShow() {
			// 检查是否有选择的背景图片
			try {
				const selectedBackgroundImage = uni.getStorageSync('selectedBackgroundImage');
				if (selectedBackgroundImage) {
					console.log('获取到背景图片URL:', selectedBackgroundImage);

					// 使用getImageInfo获取图片信息，确保图片可以被正确加载
					uni.getImageInfo({
						src: selectedBackgroundImage,
						success: (res) => {
							console.log('背景图片加载成功:', res.path);
							// 存储图片信息
							this.backgroundImageInfo = res;
							// 使用本地路径作为背景
							this.backgroundImage = res.path;
							// 立即重绘画布
							this.$nextTick(() => {
								this.drawCanvas();
							});

							// 提示成功
							uni.showToast({
								title: '背景已应用',
								icon: 'success',
								duration: 1500
							});
						},
						fail: (err) => {
							console.error('背景图片加载失败:', err);
							// 尝试直接使用URL
							this.backgroundImage = selectedBackgroundImage;
							this.backgroundImageInfo = null;
							this.drawCanvas();

							uni.showToast({
								title: '背景应用可能不完整',
								icon: 'none'
							});
						},
						complete: () => {
							// 清除存储
							uni.removeStorageSync('selectedBackgroundImage');
						}
					});
				}
			} catch (e) {
				console.error('获取背景图片失败:', e);
			}

			// 重新计算Canvas尺寸（适应屏幕变化）
			this.$nextTick(() => {
				if (this.canvasContext) {
					this.resizeCanvas();
				}
			});
		},
		data() {
			return {
				// 模版基本信息
				templateName: '',
				templateTypeIndex: 0,
				templateTypeList: Object.values(TEMPLATE_TYPES).map(type => type.name),

				// 背景设置
				backgroundImage: '',
				backgroundColor: '#FFFFFF',

				// 模板文字字段
				templateFields: deepClone(DEFAULT_TEMPLATE_FIELDS),

				// 颜色配置
				colorListByType: {
					[TEMPLATE_TYPES.THREE_COLOR.index]: TEMPLATE_TYPES.THREE_COLOR.colors,
					[TEMPLATE_TYPES.FOUR_COLOR.index]: TEMPLATE_TYPES.FOUR_COLOR.colors,
					[TEMPLATE_TYPES.SEVEN_COLOR.index]: TEMPLATE_TYPES.SEVEN_COLOR.colors
				},
				colorNameMap: COLOR_NAMES,

				// 当前颜色设置
				colorList: TEMPLATE_TYPES.THREE_COLOR.colors,
				backgroundColorList: TEMPLATE_TYPES.THREE_COLOR.colors,
				textColor: '#000000',
				showColorPicker: '',

				// 字体设置
				fontFamilyIndex: 0,
				fontFamilyList: FONT_FAMILIES,

				// 文字样式状态
				isBold: false,
				isItalic: false,
				isUnderline: false,
				isStrikethrough: false,
				fontSize: FONT_SIZE_LIMITS.DEFAULT,
				heightScale: HEIGHT_SCALE_LIMITS.DEFAULT,

				// Canvas相关
				canvasWidth: CANVAS_CONFIG.DEFAULT_WIDTH,
				canvasHeight: CANVAS_CONFIG.DEFAULT_HEIGHT,
				canvasElements: [],
				selectedElement: null,

				// 弹窗控制
				showTemplateTextPopup: false,
				showPreviewPopup: false,
				previewImagePath: '',
				previewLoading: false,

				// 错误处理
				lastError: null,
				isLoading: false,

				// UI交互增强
				showOperationTip: false,
				operationTipText: '',
				operationTipType: 'info', // 'info', 'success', 'warning', 'error'
				operationTipTimer: null,
				isDragging: false,
				dragStartTime: 0
			}
		},
		mounted() {
			this.initCanvasSize();
			this.initColorLists();
		},
		computed: {
			// 判断是否为文字颜色选择器
			isTextColorPicker() {
				return this.showColorPicker === 'text';
			},

			// 判断是否为背景颜色选择器
			isBackgroundColorPicker() {
				return this.showColorPicker === 'background';
			},

			// 当前颜色列表
			currentColorList() {
				return this.showColorPicker === 'text' ? this.colorList : this.backgroundColorList;
			}
		},

		beforeDestroy() {
			// 移除事件监听（仅在H5环境下）
			// #ifdef H5
			if (typeof window !== 'undefined') {
				window.removeEventListener('resize', this.resizeCanvas);
			}
			// #endif
		},
		methods: {
			/**
			 * 初始化Canvas尺寸
			 * 根据设备屏幕尺寸计算合适的Canvas大小
			 * @returns {void}
			 */
			initCanvasSize() {
				try {
					const deviceInfo = getDeviceInfo();
					const canvasSize = calculateCanvasSize(
						deviceInfo.windowWidth,
						0.9,
						CANVAS_CONFIG.ASPECT_RATIO
					);

					this.canvasWidth = Math.max(
						CANVAS_CONFIG.MIN_WIDTH,
						Math.min(CANVAS_CONFIG.MAX_WIDTH, canvasSize.width)
					);
					this.canvasHeight = Math.max(
						CANVAS_CONFIG.MIN_HEIGHT,
						Math.min(CANVAS_CONFIG.MAX_HEIGHT, canvasSize.height)
					);

					console.log('Canvas尺寸初始化完成:', this.canvasWidth, this.canvasHeight);
				} catch (error) {
					console.error('初始化Canvas尺寸失败:', error);
					this.handleError(formatError(error, '初始化Canvas尺寸'));

					// 使用默认尺寸
					this.canvasWidth = CANVAS_CONFIG.DEFAULT_WIDTH;
					this.canvasHeight = CANVAS_CONFIG.DEFAULT_HEIGHT;
				}
			},

			/**
			 * 初始化颜色列表
			 * 根据当前模板类型设置可用颜色
			 * @returns {void}
			 */
			initColorLists() {
				try {
					this.colorList = this.colorListByType[this.templateTypeIndex] || TEMPLATE_TYPES.THREE_COLOR.colors;
					this.backgroundColorList = [...this.colorList];
				} catch (error) {
					console.error('初始化颜色列表失败:', error);
					this.handleError(formatError(error, '初始化颜色列表'));

					// 使用默认颜色
					this.colorList = TEMPLATE_TYPES.THREE_COLOR.colors;
					this.backgroundColorList = [...this.colorList];
				}
			},

			/**
			 * 错误处理方法
			 * 统一处理应用中的错误
			 * @param {Object} error - 错误对象
			 * @returns {void}
			 */
			handleError(error) {
				this.lastError = error;
				console.error('应用错误:', error);

				// 根据错误类型显示不同的提示
				let message = MESSAGES.ERROR.CANVAS_INIT_FAILED;

				switch (error.type) {
					case ERROR_TYPES.CANVAS_INIT_ERROR:
						message = MESSAGES.ERROR.CANVAS_INIT_FAILED;
						break;
					case ERROR_TYPES.IMAGE_LOAD_ERROR:
						message = MESSAGES.ERROR.IMAGE_LOAD_FAILED;
						break;
					case ERROR_TYPES.VALIDATION_ERROR:
						message = error.message;
						break;
					default:
						message = error.message || '操作失败，请重试';
				}

				uni.showToast({
					title: message,
					icon: 'none',
					duration: 2000
				});
			},

			/**
			 * 验证模板数据
			 * @returns {Object} 验证结果
			 */
			validateTemplate() {
				const nameValidation = validateTemplateName(this.templateName);
				if (!nameValidation.valid) {
					return nameValidation;
				}

				// 验证Canvas元素
				for (const element of this.canvasElements) {
					if (element.type === ELEMENT_TYPES.TEXT) {
						if (!validateFontSize(element.fontSize)) {
							return {
								valid: false,
								message: `字体大小必须在${FONT_SIZE_LIMITS.MIN}-${FONT_SIZE_LIMITS.MAX}之间`
							};
						}

						if (!validateHeightScale(element.heightScale)) {
							return {
								valid: false,
								message: `高度拉伸值必须在${HEIGHT_SCALE_LIMITS.MIN}-${HEIGHT_SCALE_LIMITS.MAX}之间`
							};
						}

						if (!validateColor(element.color)) {
							return {
								valid: false,
								message: MESSAGES.ERROR.INVALID_COLOR
							};
						}
					}
				}

				return { valid: true, message: '' };
			},

			// Canvas事件处理
			onElementSelected(element) {
				this.selectedElement = element;
				if (element) {
					this.updateInspector();
					this.showOperationTip(`已选中${element.type === 'text' ? '文本' : '图片'}元素`, 'info', 1500);
				} else {
					this.showOperationTip('点击元素进行选择', 'info', 1000);
				}
			},

			onElementUpdated() {
				// 元素已在Canvas组件中更新，这里可以添加额外逻辑
			},

			onElementDeleted(element) {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除这个${element.type === 'text' ? '文本' : '图片'}元素吗？`,
					confirmColor: '#f44336',
					success: (res) => {
						if (res.confirm) {
							const index = this.canvasElements.findIndex(item => item.id === element.id);
							if (index !== -1) {
								this.canvasElements.splice(index, 1);
								this.selectedElement = null;
								this.showOperationTip('元素已删除', 'success', 1500);
							}
						}
					}
				});
			},

			// 拖拽事件处理
			onDragStart() {
				this.isDragging = true;
				this.dragStartTime = Date.now();
				this.showOperationTip('开始拖拽...', 'info', 0); // 0表示不自动隐藏
			},

			onDragMove(data) {
				if (this.isDragging) {
					const duration = Date.now() - this.dragStartTime;
					if (duration > 500) { // 拖拽超过500ms显示坐标信息
						this.showOperationTip(
							`位置: (${Math.round(data.position.x)}, ${Math.round(data.position.y)})`,
							'info',
							0
						);
					}
				}
			},

			onDragEnd(data) {
				this.isDragging = false;
				const duration = data.duration || 0;
				const moved = data.startPosition && (
					Math.abs(data.endPosition.x - data.startPosition.x) > 5 ||
					Math.abs(data.endPosition.y - data.startPosition.y) > 5
				);

				if (moved) {
					this.showOperationTip(
						`拖拽完成 (${duration}ms)`,
						'success',
						1500
					);
				} else {
					this.hideOperationTip();
				}
			},

			// 长按事件处理
			onElementLongPress(element) {
				this.showOperationTip(`长按${element.type === 'text' ? '文本' : '图片'}元素`, 'info', 1500);
			},

			// 复制事件处理
			onElementCopy(element) {
				try {
					// 创建元素副本
					const copiedElement = {
						...element,
						id: this.generateId(),
						x: element.x + 20, // 偏移位置避免重叠
						y: element.y + 20
					};

					// 添加到画布
					this.canvasElements.push(copiedElement);

					// 选中新复制的元素
					this.selectedElement = copiedElement;

					this.showOperationTip('元素已复制', 'success', 1500);
				} catch (error) {
					console.error('复制元素失败:', error);
					this.showOperationTip('复制失败', 'error', 2000);
				}
			},

			// Canvas错误处理
			onCanvasError(error) {
				console.error('Canvas错误:', error);
				this.showOperationTip('Canvas操作出错', 'error', 2000);
			},
			
			updateInspector() {
				if (this.selectedElement) {
					const el = this.selectedElement;

					if (el.type === 'text') {
						// 设置字体
						this.fontFamilyIndex = this.fontFamilyList.indexOf(el.fontFamily || '微软雅黑');
						if (this.fontFamilyIndex === -1) this.fontFamilyIndex = 0;

						// 设置颜色
						this.textColor = el.color || '#000000';

						// 设置文本样式
						this.isBold = !!el.isBold;
						this.isItalic = !!el.isItalic;
						this.isUnderline = !!el.isUnderline;
						this.isStrikethrough = !!el.isStrikethrough;

						// 设置字号和高度拉伸
						this.fontSize = parseInt(el.fontSize || 16);
						this.heightScale = parseFloat(el.heightScale || 1.0);
					}
				}
			},

			// 字体相关方法
			onFontFamilyChange(e) {
				this.fontFamilyIndex = e.detail.value;
				this.updateSelectedElement('fontFamily', this.fontFamilyList[this.fontFamilyIndex]);
			},


			// 文字样式切换
			toggleBold() {
				this.isBold = !this.isBold;
				this.updateSelectedElement('isBold', this.isBold);
			},

			toggleItalic() {
				this.isItalic = !this.isItalic;
				this.updateSelectedElement('isItalic', this.isItalic);
			},

			toggleUnderline() {
				this.isUnderline = !this.isUnderline;
				this.updateSelectedElement('isUnderline', this.isUnderline);
			},

			toggleStrikethrough() {
				this.isStrikethrough = !this.isStrikethrough;
				this.updateSelectedElement('isStrikethrough', this.isStrikethrough);
			},

			// 字号调整
			increaseFontSize() {
				if (this.fontSize < 80) {
					this.fontSize = parseInt(this.fontSize) + 2;
					this.updateSelectedElement('fontSize', this.fontSize);
				}
			},

			decreaseFontSize() {
				if (this.fontSize > 8) {
					this.fontSize = parseInt(this.fontSize) - 2;
					this.updateSelectedElement('fontSize', this.fontSize);
				}
			},

			onFontSizeChange(e) {
				const value = e.detail ? e.detail.value : e;
				this.fontSize = parseInt(value);
				this.updateSelectedElement('fontSize', this.fontSize);
			},

			// 高度拉伸调整
			increaseHeightScale() {
				if (this.heightScale < 2.0) {
					this.heightScale = Math.round((this.heightScale + 0.1) * 10) / 10;
					this.updateSelectedElement('heightScale', this.heightScale);
				}
			},

			decreaseHeightScale() {
				if (this.heightScale > 0.5) {
					this.heightScale = Math.round((this.heightScale - 0.1) * 10) / 10;
					this.updateSelectedElement('heightScale', this.heightScale);
				}
			},

			onHeightScaleChange(e) {
				const value = e.detail ? e.detail.value : e;
				this.heightScale = parseFloat(value);
				this.updateSelectedElement('heightScale', this.heightScale);
			},

			// 颜色选择
			openTextColorPicker() {
				this.showColorPicker = 'text';
			},

			selectColor(color) {
				if (this.showColorPicker === 'text') {
					if (!this.colorList.includes(color)) {
						uni.showToast({
							title: `当前模板类型不支持该颜色`,
							icon: 'none',
							duration: 1500
						});
						return;
					}

					this.textColor = color;
					this.updateSelectedElement('color', color);
				} else if (this.showColorPicker === 'background') {
					if (!this.backgroundColorList.includes(color)) {
						uni.showToast({
							title: `当前模板类型不支持该背景颜色`,
							icon: 'none',
							duration: 1500
						});
						return;
					}

					this.backgroundColor = color;
					this.backgroundImage = '';
				}
				this.showColorPicker = '';
			},

			// 更新选中元素
			updateSelectedElement(property, value) {
				if (property === 'color' && !this.colorList.includes(value)) {
					uni.showToast({
						title: `当前模板类型不支持该颜色`,
						icon: 'none',
						duration: 1500
					});
					return;
				}

				if (this.selectedElement) {
					this.selectedElement[property] = value;

					if (property === 'fontSize') {
						this.selectedElement[property] = parseInt(value);
						this.fontSize = this.selectedElement[property];
					} else if (property === 'heightScale') {
						this.selectedElement[property] = parseFloat(value);
						this.heightScale = this.selectedElement[property];
					}

					// 同步UI状态
					if (property === 'isBold') this.isBold = value;
					if (property === 'isItalic') this.isItalic = value;
					if (property === 'isUnderline') this.isUnderline = value;
					if (property === 'isStrikethrough') this.isStrikethrough = value;
					if (property === 'fontFamily') {
						this.fontFamilyIndex = this.fontFamilyList.indexOf(value);
					}
					if (property === 'color') {
						this.textColor = value;
					}
				} else {
					// 批量更新所有文本元素
					if (property === 'fontSize') this.fontSize = parseInt(value);
					else if (property === 'heightScale') this.heightScale = parseFloat(value);
					else if (property === 'isBold') this.isBold = value;
					else if (property === 'isItalic') this.isItalic = value;
					else if (property === 'isUnderline') this.isUnderline = value;
					else if (property === 'isStrikethrough') this.isStrikethrough = value;
					else if (property === 'fontFamily') this.fontFamilyIndex = this.fontFamilyList.indexOf(value);
					else if (property === 'color') this.textColor = value;

					this.canvasElements.forEach(element => {
						if (element.type === 'text') {
							element[property] = value;
							if (property === 'fontSize') element[property] = parseInt(value);
							else if (property === 'heightScale') element[property] = parseFloat(value);
						}
					});
				}
			},
			
			// 显示批量操作选项
			showBatchOptions() {
				uni.showToast({
					title: '批量操作功能开发中',
					icon: 'none'
				});
			},

			// 水平居中所有元素
			centerAllElements() {
				// 检查是否有文本元素
				const textElements = this.canvasElements.filter(element => element.type === 'text');
				if (textElements.length === 0) {
					uni.showToast({
						title: '没有可居中的文本元素',
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				// 显示对齐方式选择
				uni.showActionSheet({
					itemList: ['水平居中', '左对齐', '右对齐', '统一文本对齐方式'],
					success: (res) => {
						let alignValue;
						let textAlignValue;
						let message;
						
						switch(res.tapIndex) {
							case 0: // 水平居中
								alignValue = 'center';
								message = '水平居中';
								break;
							case 1: // 左对齐
								alignValue = 'left';
								message = '左对齐';
								break;
							case 2: // 右对齐
								alignValue = 'right';
								message = '右对齐';
								break;
							case 3: // 统一文本对齐方式
								// 二级菜单，选择文本内部对齐方式
								uni.showActionSheet({
									itemList: ['左对齐', '居中对齐', '右对齐'],
									success: (subRes) => {
										switch(subRes.tapIndex) {
											case 0:
												textAlignValue = 'left';
												message = '文本左对齐';
												break;
											case 1:
												textAlignValue = 'center';
												message = '文本居中对齐';
												break;
											case 2:
												textAlignValue = 'right';
												message = '文本右对齐';
												break;
											default:
												return;
										}
										
										// 应用文本内部对齐
										let updated = false;
										textElements.forEach(element => {
											element.textAlign = textAlignValue;
											updated = true;
										});
										
										if (updated) {
											this.drawCanvas();
											uni.showToast({
												title: `已应用${message}`,
												icon: 'success',
												duration: 1500
											});
										}
									}
								});
								return;
							default:
								return;
						}
						
						// 应用水平位置对齐
						if (alignValue) {
							let updated = false;
							
							textElements.forEach(element => {
								const box = this.getElementBoundingBox(element);
								let newX = element.x;
								
								// 根据选择的对齐方式设置新的水平位置
								if (alignValue === 'left') {
									newX = 0; // 左对齐到画布左边缘
								} else if (alignValue === 'center') {
									// 居中对齐
									if (element.textAlign === 'center') {
										newX = this.canvasWidth / 2;
									} else if (element.textAlign === 'right') {
										newX = this.canvasWidth / 2 + box.width / 2;
									} else {
										newX = (this.canvasWidth - box.width) / 2;
									}
								} else if (alignValue === 'right') {
									// 右对齐
									if (element.textAlign === 'right') {
										newX = this.canvasWidth;
									} else if (element.textAlign === 'center') {
										newX = this.canvasWidth - box.width / 2;
									} else {
										newX = this.canvasWidth - box.width;
									}
								}
								
								// 更新元素位置
								element.x = newX;
								updated = true;
							});
							
							if (updated) {
								this.drawCanvas();
								uni.showToast({
									title: `已应用${message}`,
									icon: 'success',
									duration: 1500
								});
							}
						}
					}
				});
			},
			
			autoAdjustSpacing() {
				// 检查是否有文本元素
				const textElements = this.canvasElements.filter(element => element.type === 'text');
				if (textElements.length <= 1) {
					uni.showToast({
						title: '需要至少两个文本元素',
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				// 显示行间距模式选择
				uni.showActionSheet({
					itemList: ['均匀分布', '紧凑排列', '宽松排列'],
					success: (res) => {
						// 按Y坐标排序文本元素（从上到下）
						const sortedElements = [];
						for (let i = 0; i < textElements.length; i++) {
							sortedElements.push(textElements[i]);
						}
						sortedElements.sort((a, b) => {
							// 获取元素的边界框
							const boxA = this.getElementBoundingBox(a);
							const boxB = this.getElementBoundingBox(b);
							return boxA.y - boxB.y;
						});
						
						// 计算可用的垂直空间
						const totalHeight = this.canvasHeight;
						
						// 计算所有元素的总高度
						let totalElementsHeight = 0;
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							totalElementsHeight += box.height;
						});
						
						// 根据选择的模式设置间距
						let spacingFactor;
						let spacingMode;
						
						switch(res.tapIndex) {
							case 0: // 均匀分布
								spacingFactor = 1;
								spacingMode = '均匀分布';
								break;
							case 1: // 紧凑排列
								spacingFactor = 0.5;
								spacingMode = '紧凑排列';
								break;
							case 2: // 宽松排列
								spacingFactor = 1.5;
								spacingMode = '宽松排列';
								break;
							default:
								spacingFactor = 1;
								spacingMode = '均匀分布';
						}
						
						// 计算每个元素之间的间距（根据选择的模式）
						const elementCount = sortedElements.length;
						const baseSpacing = (totalHeight - totalElementsHeight) / (elementCount + 1);
						const spacing = baseSpacing * spacingFactor;
						
						// 确保间距不小于最小值
						const minSpacing = 10; // 最小间距为10像素
						const actualSpacing = Math.max(spacing, minSpacing);
						
						// 如果使用宽松排列且元素太多，可能需要调整位置以确保所有元素都在画布内
						let startY = actualSpacing;
						const totalSpaceNeeded = totalElementsHeight + actualSpacing * (elementCount + 1);
						
						if (totalSpaceNeeded > totalHeight) {
							// 如果总空间超出画布高度，调整起始位置
							startY = Math.max(minSpacing, (totalHeight - totalElementsHeight - actualSpacing * (elementCount - 1)) / 2);
						}
						
						// 重新定位所有元素
						let currentY = startY; // 从顶部间距开始
						
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							
							// 计算新的Y坐标
							let newY;
							if (element.type === 'text') {
								// 文本元素的Y坐标是基线位置
								newY = currentY + box.height;
							} else {
								newY = currentY;
							}
							
							// 更新元素位置
							element.y = newY;
							
							// 更新当前Y坐标
							currentY += box.height + actualSpacing;
						});
						
						// 重绘画布
						this.drawCanvas();
						
						uni.showToast({
							title: `已应用${spacingMode}间距`,
							icon: 'success',
							duration: 1500
						});
					}
				});
			},
			beautifyAllElements() {
				// 检查是否有文本元素
				const textElements = this.canvasElements.filter(element => element.type === 'text');
				if (textElements.length === 0) {
					uni.showToast({
						title: '没有可美化的文本元素',
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				// 显示美化模式选择
				uni.showActionSheet({
					itemList: ['标准美化', '简约风格', '强调风格'],
					success: (res) => {
						// 按Y坐标排序文本元素（从上到下）
						const sortedElements = [];
						for (let i = 0; i < textElements.length; i++) {
							sortedElements.push(textElements[i]);
						}
						sortedElements.sort((a, b) => {
							// 获取元素的边界框
							const boxA = this.getElementBoundingBox(a);
							const boxB = this.getElementBoundingBox(b);
							return boxA.y - boxB.y;
						});
						
						// 根据选择的美化模式设置参数
						let spacingFactor, fontSizeMultiplier, mainColor, titleFontSize;
						let mode; 
						switch(res.tapIndex) {
							case 0: // 标准美化
								spacingFactor = 1;
								fontSizeMultiplier = 1;
								mainColor = '#333333';
								titleFontSize = 24;
								mode = '标准美化';
								break;
							case 1: // 简约风格
								spacingFactor = 0.8;
								fontSizeMultiplier = 0.9;
								mainColor = '#555555';
								titleFontSize = 22;
								mode = '简约风格';
								break;
							case 2: // 强调风格
								spacingFactor = 1.2;
								fontSizeMultiplier = 1.1;
								mainColor = '#000000';
								titleFontSize = 28;
								mode = '强调风格';
								break;
							default:
								spacingFactor = 1;
								fontSizeMultiplier = 1;
								mainColor = '#333333';
								titleFontSize = 24;
								mode = '标准美化';
						}
						
						// 1. 水平居中所有文本元素
						sortedElements.forEach((element, index) => {
							// 获取元素的边界框
							const box = this.getElementBoundingBox(element);
							
							// 设置水平居中
							if (element.textAlign === 'center') {
								element.x = this.canvasWidth / 2;
							} else if (element.textAlign === 'right') {
								element.x = this.canvasWidth / 2 + box.width / 2;
							} else {
								element.x = (this.canvasWidth - box.width) / 2;
							}
							
							// 2. 设置统一的文本对齐方式为居中
							element.textAlign = 'center';
							
							// 3. 根据位置设置不同的字体大小和样式
							if (index === 0) {
								// 第一个元素（标题）
								element.fontSize = titleFontSize;
								element.isBold = true;
								element.color = mainColor;
							} else if (index === 1) {
								// 第二个元素（副标题）
								element.fontSize = Math.floor(titleFontSize * 0.8);
								element.isBold = false;
								element.color = mainColor;
							} else {
								// 其他元素
								element.fontSize = Math.floor(titleFontSize * 0.7);
								element.color = mainColor;
							}
							
							// 应用字体大小乘数
							element.fontSize = Math.floor(element.fontSize * fontSizeMultiplier);
						});
						
						// 4. 调整元素间距
						// 计算可用的垂直空间
						const totalHeight = this.canvasHeight;
						
						// 计算所有元素的总高度
						let totalElementsHeight = 0;
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							totalElementsHeight += box.height;
						});
						
						// 计算每个元素之间的间距
						const elementCount = sortedElements.length;
						const spacing = ((totalHeight - totalElementsHeight) / (elementCount + 1)) * spacingFactor;
						
						// 确保间距不小于最小值
						const minSpacing = 10;
						const actualSpacing = Math.max(spacing, minSpacing);
						
						// 重新定位所有元素
						let currentY = actualSpacing;
						
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							
							// 计算新的Y坐标
							let newY;
							if (element.type === 'text') {
								newY = currentY + box.height;
							} else {
								newY = currentY;
							}
							
							// 更新元素位置
							element.y = newY;
							
							// 更新当前Y坐标
							currentY += box.height + actualSpacing;
						});
						
						// 5. 更新UI状态
						this.textAlignIndex = this.textAlignList.findIndex(item => item.value === 'center');
						this.textColor = mainColor;
						
						// 6. 重绘画布
						this.drawCanvas();
						
						uni.showToast({
							title: `已应用${mode}`,
							icon: 'success',
							duration: 1500
						});
					}
				});
			},
			previewTemplate() {
				// 设置加载状态
				this.previewLoading = true;
				this.previewImagePath = '';

				// 确保画布已经绘制完成
				this.drawCanvas();

				// 延迟一下再生成图片，确保Canvas已经完全渲染
				setTimeout(() => {
					// 将Canvas转换为图片
					uni.canvasToTempFilePath({
						canvasId: 'editCanvas',
						success: (res) => {
							console.log('生成预览图片成功:', res.tempFilePath);
							this.previewImagePath = res.tempFilePath;
							this.previewLoading = false;

							// 打开预览弹窗
							this.showPreviewPopup = true;
						},
						fail: (err) => {
							console.error('生成预览图片失败:', err);
							this.previewLoading = false;

							// 显示错误提示
							uni.showToast({
								title: '预览生成失败',
								icon: 'none',
								duration: 2000
							});
						}
					}, this);
				}, 200);
			},
			closePreview() {
				this.showPreviewPopup = false;
			},
			savePreviewImage() {
				if (!this.previewImagePath) {
					uni.showToast({
						title: '预览图片不存在',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				
				// 显示加载提示
				uni.showLoading({
					title: '保存中...'
				});
				
				// 保存图片到相册
				uni.saveImageToPhotosAlbum({
					filePath: this.previewImagePath,
					success: () => {
						uni.hideLoading();
						uni.showToast({
							title: '图片已保存到相册',
							icon: 'success',
							duration: 2000
						});
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('保存图片失败:', err);
						
						// 检查是否是因为用户拒绝授权
						if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize') >= 0) {
							uni.showModal({
								title: '提示',
								content: '保存图片需要授权，是否去设置页面授权？',
								success: (res) => {
									if (res.confirm) {
										// 打开设置页面
										uni.openSetting();
									}
								}
							});
						} else {
							uni.showToast({
								title: '保存失败',
								icon: 'none',
								duration: 2000
							});
						}
					}
				});
			},
			/**
			 * 模板类型变更处理
			 * 更新颜色列表并验证现有元素
			 * @param {Object} e - 事件对象
			 * @returns {void}
			 */
			onTemplateTypeChange(e) {
				try {
					const newTypeIndex = parseInt(e.detail.value);

					// 验证新的类型索引
					if (!this.colorListByType[newTypeIndex]) {
						throw new Error(`无效的模板类型索引: ${newTypeIndex}`);
					}

					this.templateTypeIndex = newTypeIndex;
					this.colorList = this.colorListByType[newTypeIndex];
					this.backgroundColorList = [...this.colorList];

					// 检查当前文本颜色是否在新的颜色列表中
					if (!this.colorList.includes(this.textColor)) {
						this.textColor = this.colorList[0];
						console.log('文本颜色已更新为:', this.textColor);

						// 如果有选中的文本元素，也更新元素的颜色
						if (this.selectedElement && this.selectedElement.type === ELEMENT_TYPES.TEXT) {
							this.selectedElement.color = this.textColor;
						}
					}

					// 检查背景颜色是否在新的颜色列表中
					if (!this.backgroundColorList.includes(this.backgroundColor)) {
						this.backgroundColor = this.backgroundColorList[0];
						console.log('背景颜色已更新为:', this.backgroundColor);
					}

					// 更新所有文本元素的颜色，确保它们在新的颜色列表中
					let updatedCount = 0;
					this.canvasElements.forEach(element => {
						if (element.type === ELEMENT_TYPES.TEXT && !this.colorList.includes(element.color)) {
							element.color = this.colorList[0];
							updatedCount++;
						}
					});

					// 显示提示
					const typeName = this.templateTypeList[newTypeIndex];
					let message = `已切换至${typeName}`;
					if (updatedCount > 0) {
						message += `，已更新${updatedCount}个文本元素的颜色`;
					}

					uni.showToast({
						title: message,
						icon: 'none',
						duration: 1500
					});

				} catch (error) {
					console.error('模板类型变更失败:', error);
					this.handleError(formatError(error, '模板类型变更'));
				}
			},
			// 颜色选择器相关方法
			openTextColorPicker() {
				this.showColorPicker = 'text';
			},

			closeColorPicker() {
				this.showColorPicker = '';
			},

			// 背景相关方法
			setSolidBackground() {
				this.showColorPicker = 'background';
			},
			
			// 清除背景
			clearBackground() {
				this.backgroundImage = '';
				this.backgroundColor = '#FFFFFF';
				this.drawCanvas();
				
				uni.showToast({
					title: '已清除背景',
					icon: 'success',
					duration: 1500
				});
			},
			
			// 选择背景图片
			selectBackground() {
				// 跳转到背景列表页面
				uni.navigateTo({
					url: '/pages/template/backgroundList?select=true'
				});
			},
			
			// 插入模板文字
			insertTemplateText() {
				// 打开模板文字弹窗
				this.showTemplateTextPopup = true;
			},

			// 关闭模板文字弹窗
			onTemplatePopupClose() {
				this.showTemplateTextPopup = false;
			},

			// 确认添加模板文字
			confirmAddTemplateText() {
				// 添加姓名文本
				if (this.templateFields.name) {
					this.addTextElement(this.templateFields.name, 24, true);
				}
				
				// 添加职位文本
				if (this.templateFields.position) {
					this.addTextElement(this.templateFields.position, 18);
				}
				
				// 添加公司文本
				if (this.templateFields.company) {
					this.addTextElement(this.templateFields.company, 16);
				}
				
				// 添加其他信息文本
				if (this.templateFields.other) {
					this.addTextElement(this.templateFields.other, 14);
				}
				
				// 关闭弹窗
				this.showTemplateTextPopup = false;
				
				// 提示成功
				uni.showToast({
					title: '已添加模板文字',
					icon: 'success',
					duration: 1500
				});
			},
			
			// 插入固定文字
			insertFixedText() {
				uni.showModal({
					title: '添加文本',
					editable: true,
					placeholderText: '请输入文本内容',
					success: (res) => {
						if (res.confirm && res.content && res.content.trim() !== '') {
							this.addTextElement(res.content.trim());
						}
					}
				});
			},
			
			/**
			 * 添加文本元素
			 * @param {string} text - 文本内容
			 * @param {number} fontSize - 字体大小
			 * @param {boolean} isBold - 是否粗体
			 * @returns {void}
			 */
			addTextElement(text, fontSize = FONT_SIZE_LIMITS.DEFAULT, isBold = false) {
				try {
					// 参数验证
					if (!text || typeof text !== 'string' || text.trim() === '') {
						throw new Error('文本内容不能为空');
					}

					if (!validateFontSize(fontSize)) {
						fontSize = FONT_SIZE_LIMITS.DEFAULT;
						console.warn('字体大小无效，使用默认值:', fontSize);
					}

					// 生成唯一ID
					const id = generateUniqueId();

					// 计算文本位置 - 默认放在画布中心
					const x = this.canvasWidth / 2;
					const y = this.canvasHeight / 2;

					// 创建文本元素
					const textElement = {
						id,
						type: ELEMENT_TYPES.TEXT,
						text: text.trim(),
						x,
						y,
						fontSize,
						fontFamily: this.fontFamilyList[this.fontFamilyIndex] || FONT_FAMILIES[0],
						color: this.textColor,
						textAlign: 'center',
						isBold: !!isBold,
						isItalic: false,
						isUnderline: false,
						isStrikethrough: false,
						heightScale: HEIGHT_SCALE_LIMITS.DEFAULT
					};

					// 检查元素数量限制
					if (this.canvasElements.length >= 50) {
						uni.showToast({
							title: MESSAGES.WARNING.PERFORMANCE_WARNING,
							icon: 'none',
							duration: 2000
						});
					}

					// 添加到元素数组
					this.canvasElements.push(textElement);

					// 选中新添加的元素
					this.selectedElement = textElement;

					// 更新属性面板
					this.updateInspector();

					console.log('文本元素添加成功:', textElement);

				} catch (error) {
					console.error('添加文本元素失败:', error);
					this.handleError(formatError(error, '添加文本元素'));
				}
			},
			
			// 插入图片
			insertImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						const imagePath = res.tempFilePaths[0];
						
						// 获取图片信息
						uni.getImageInfo({
							src: imagePath,
							success: (imageInfo) => {
								// 生成唯一ID
								const id = 'image_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
								
								// 计算图片尺寸，保持宽高比
								const maxWidth = this.canvasWidth * 0.8;
								const maxHeight = this.canvasHeight * 0.8;
								
								let width = imageInfo.width;
								let height = imageInfo.height;
								
								// 如果图片太大，按比例缩小
								if (width > maxWidth || height > maxHeight) {
									const ratio = Math.min(maxWidth / width, maxHeight / height);
									width = Math.floor(width * ratio);
									height = Math.floor(height * ratio);
								}
								
								// 计算图片位置 - 默认放在画布中心
								const x = (this.canvasWidth - width) / 2;
								const y = (this.canvasHeight - height) / 2;
								
								// 创建图片元素
								const imageElement = {
									id,
									type: 'image',
									src: imagePath,
									x,
									y,
									width,
									height,
									rotation: 0 // 初始旋转角度为0
								};
								
								// 添加到元素数组
								this.canvasElements.push(imageElement);
								
								// 选中新添加的元素
								this.selectedElement = imageElement;
								
								// 重绘画布
								this.drawCanvas();
								
								// 提示成功
								uni.showToast({
									title: '已添加图片',
									icon: 'success',
									duration: 1500
								});
							},
							fail: (err) => {
								console.error('获取图片信息失败:', err);
								uni.showToast({
									title: '添加图片失败',
									icon: 'none',
									duration: 1500
								});
							}
						});
					}
				});
			},
			
			// 选择元素
			selectElementAt(x, y) {
				// 首先清除当前选中状态
				this.selectedElement = null;
				
				// 从后往前遍历元素，这样可以优先选择最上层的元素
				for (let i = this.canvasElements.length - 1; i >= 0; i--) {
					const element = this.canvasElements[i];
					const box = this.getElementBoundingBox(element);
					
					// 检查点击位置是否在元素的边界框内
					if (x >= box.x && x <= box.x + box.width && y >= box.y && y <= box.y + box.height) {
						// 找到了被点击的元素
						this.selectedElement = element;
						break;
					}
				}
				
				// 重绘画布，显示选中状态
				this.drawCanvas();
				
				return this.selectedElement;
			},
			
			/**
			 * 保存模板
			 * 验证模板数据并保存到本地存储
			 * @returns {void}
			 */
			saveTemplate() {
				try {
					// 验证模板数据
					const validation = this.validateTemplate();
					if (!validation.valid) {
						uni.showModal({
							title: '验证失败',
							content: validation.message,
							showCancel: false
						});
						return;
					}

					// 检查是否有元素
					if (this.canvasElements.length === 0) {
						uni.showModal({
							title: '提示',
							content: '请至少添加一个元素',
							showCancel: false
						});
						return;
					}

					this.isLoading = true;

					// 显示加载提示
					uni.showLoading({
						title: '保存中...'
					});

					// 将Canvas转换为图片
					uni.canvasToTempFilePath({
						canvasId: 'editCanvas',
						success: (res) => {
							this.handleSaveSuccess(res);
						},
						fail: (err) => {
							this.handleSaveError(err);
						}
					}, this);

				} catch (error) {
					console.error('保存模板失败:', error);
					this.handleError(formatError(error, '保存模板'));
					this.isLoading = false;
					uni.hideLoading();
				}
			},

			/**
			 * 处理保存成功
			 * @param {Object} res - Canvas转换结果
			 * @returns {void}
			 */
			handleSaveSuccess(res) {
				try {
					console.log('生成模板图片成功:', res.tempFilePath);

					// 构建模板数据
					const templateData = {
						id: generateUniqueId(),
						name: this.templateName.trim(),
						type: this.templateTypeIndex,
						typeName: this.templateTypeList[this.templateTypeIndex],
						backgroundColor: this.backgroundColor,
						backgroundImage: this.backgroundImage,
						elements: deepClone(this.canvasElements), // 深拷贝避免引用问题
						preview: res.tempFilePath,
						createTime: new Date().toISOString(),
						version: '2.0.0'
					};

					// 获取已有模板
					const templates = safeExecute(() => {
						return uni.getStorageSync('templates') || [];
					}, [], '获取已有模板');

					// 添加新模板
					templates.push(templateData);

					// 保存到本地存储
					safeExecute(() => {
						uni.setStorageSync('templates', templates);
					}, null, '保存模板到本地存储');

					// 隐藏加载提示
					uni.hideLoading();
					this.isLoading = false;

					// 显示成功提示
					uni.showToast({
						title: MESSAGES.SUCCESS.TEMPLATE_SAVED,
						icon: 'success',
						duration: 2000
					});

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);

				} catch (error) {
					this.handleSaveError(error);
				}
			},

			/**
			 * 处理保存错误
			 * @param {Error} error - 错误对象
			 * @returns {void}
			 */
			handleSaveError(error) {
				console.error('保存模板失败:', error);
				uni.hideLoading();
				this.isLoading = false;

				this.handleError(formatError(error, '保存模板'));

				uni.showModal({
					title: '保存失败',
					content: error.message || MESSAGES.ERROR.TEMPLATE_SAVE_ERROR,
					showCancel: false
				});
			},
			
			/**
			 * 高度拉伸变更处理
			 * @param {Object} e - 事件对象
			 * @returns {void}
			 */
			onHeightScaleChange(e) {
				try {
					const value = e.detail ? e.detail.value : e;
					const heightScale = parseFloat(value);

					if (!validateHeightScale(heightScale)) {
						console.warn('高度拉伸值无效:', heightScale);
						return;
					}

					this.heightScale = heightScale;
					this.updateSelectedElement('heightScale', this.heightScale);
				} catch (error) {
					console.error('高度拉伸变更失败:', error);
				}
			},

			/**
			 * 增加高度拉伸
			 * @returns {void}
			 */
			increaseHeightScale() {
				const newScale = Math.min(
					HEIGHT_SCALE_LIMITS.MAX,
					parseFloat((this.heightScale + HEIGHT_SCALE_LIMITS.STEP).toFixed(1))
				);

				if (newScale !== this.heightScale) {
					this.heightScale = newScale;
					this.updateSelectedElement('heightScale', this.heightScale);
				}
			},

			/**
			 * 减少高度拉伸
			 * @returns {void}
			 */
			decreaseHeightScale() {
				const newScale = Math.max(
					HEIGHT_SCALE_LIMITS.MIN,
					parseFloat((this.heightScale - HEIGHT_SCALE_LIMITS.STEP).toFixed(1))
				);

				if (newScale !== this.heightScale) {
					this.heightScale = newScale;
					this.updateSelectedElement('heightScale', this.heightScale);
				}
			},

			/**
			 * 显示操作提示
			 * @param {string} text - 提示文本
			 * @param {string} type - 提示类型
			 * @param {number} duration - 显示时长，0表示不自动隐藏
			 * @returns {void}
			 */
			showOperationTip(text, type = 'info', duration = 2000) {
				// 清除之前的定时器
				if (this.operationTipTimer) {
					clearTimeout(this.operationTipTimer);
					this.operationTipTimer = null;
				}

				this.operationTipText = text;
				this.operationTipType = type;
				this.showOperationTip = true;

				// 设置自动隐藏
				if (duration > 0) {
					this.operationTipTimer = setTimeout(() => {
						this.hideOperationTip();
					}, duration);
				}
			},

			/**
			 * 隐藏操作提示
			 * @returns {void}
			 */
			hideOperationTip() {
				this.showOperationTip = false;
				if (this.operationTipTimer) {
					clearTimeout(this.operationTipTimer);
					this.operationTipTimer = null;
				}
			}
		}
	}
</script>

<style scoped>
	.edit-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f9fa;
		overflow: hidden;
	}

	/* Canvas画布区域 */
	.canvas-section {
		flex: 0 0 33.33vh; /* 固定占屏幕高度的1/3 */
		background-color: #ffffff;
		position: relative;
		display: flex;
		flex-direction: column;
	}

	.canvas-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 8px 15px;
		background-color: #f8f9fa;
		border-bottom: 1px solid #e0e0e0;
		min-height: 40px;
	}

	.canvas-title {
		font-size: 16px;
		font-weight: 600;
		color: #24292f;
		max-width: 60%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.canvas-info {
		display: flex;
		gap: 15px;
		align-items: center;
	}

	.canvas-size,
	.element-count {
		font-size: 12px;
		color: #656d76;
		background-color: #ffffff;
		padding: 4px 8px;
		border-radius: 12px;
		border: 1px solid #d0d7de;
	}

	.canvas-container {
		flex: 1;
		padding: 10px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #ffffff;
	}

	.edit-canvas {
		background-color: #f5f5f5;
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/* 操作提示 */
	.operation-tip {
		position: absolute;
		top: 50px;
		left: 50%;
		transform: translateX(-50%);
		padding: 8px 16px;
		border-radius: 20px;
		font-size: 14px;
		font-weight: 500;
		z-index: 100;
		animation: tipFadeIn 0.3s ease-out;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
	}

	.operation-tip.info {
		background-color: #0969da;
		color: white;
	}

	.operation-tip.success {
		background-color: #1a7f37;
		color: white;
	}

	.operation-tip.warning {
		background-color: #fb8500;
		color: white;
	}

	.operation-tip.error {
		background-color: #da3633;
		color: white;
	}

	.tip-text {
		font-size: 14px;
		line-height: 1.2;
	}

	@keyframes tipFadeIn {
		from {
			opacity: 0;
			transform: translateX(-50%) translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateX(-50%) translateY(0);
		}
	}
	
	/* 分隔线 */
	.divider {
		height: 1px;
		background-color: #e0e0e0;
		width: 100%;
	}
	
	/* 属性设置界面区域 */
	.property-section {
		flex: 1;
		padding: 10px;
		background-color: #ffffff;
		overflow-y: auto;
		overflow-x: hidden;
		-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
		height: 66.67vh; /* 占屏幕高度的2/3 */
		box-sizing: border-box;
	}
	
	/* 模版名称区域 */
	.template-name-section {
		margin-bottom: 20px;
		padding: 0 5px;
	}

	.template-name-input {
		/* width: 100%; */
		height: 40px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		padding: 0 12px;
		font-size: 14px;
		background-color: #f6f8fa;
	}

	/* 模板类型区域 */
	.template-type-section {
		margin-bottom: 20px;
		padding: 0 5px;
		display: flex;
		align-items: center;
	}

	.template-type-label {
		width: 80px;
		font-size: 14px;
		color: #24292f;
		flex-shrink: 0;
	}

	.template-type-picker {
		flex: 1;
		margin-left: 10px;
	}
	
	/* 属性设置标题 */
	.property-title {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20px;
		color: #24292f;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.property-subtitle {
		font-size: 14px;
		font-weight: normal;
		color: #57606a;
		margin-top: 5px;
	}
	
	/* 属性组 */
	.property-group {
		margin-bottom: 30px;
		padding-bottom: 20px;
		overflow: visible;
	}
	
	/* 属性项 */
	.property-item {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		padding: 10px 5px;
		flex-wrap: wrap; /* 允许在小屏幕上换行 */
	}
	
	.property-label {
		width: 80px;
		font-size: 14px;
		color: #24292f;
		flex-shrink: 0;
	}
	
	/* 选择器样式 */
	.property-picker {
		flex: 1;
		margin-left: 10px;
	}
	
	.picker-view {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 35px;
		line-height: 35px;
		padding: 0 12px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		background-color: #f6f8fa;
		font-size: 14px;
		color: #656d76;
	}
	
	.picker-arrow {
		font-size: 12px;
	}
	
	/* 颜色选择器 */
	.color-picker {
		flex: 1;
		margin-left: 10px;
		display: flex;
		align-items: center;
	}
	
	.color-preview {
		width: 35px;
		height: 35px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		cursor: pointer;
		margin-right: 10px;
	}

	.color-name {
		flex: 1;
		font-size: 14px;
		color: #24292f;
	}
	
	/* 位置控制 */
	.position-controls {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 10px;
		gap: 8px;
	}
	
	.position-icon {
		width: 30px;
		height: 30px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f6f8fa;
		cursor: pointer;
		font-size: 12px;
	}
	
	.position-icon.active {
		background-color: #0969da;
		color: white;
		border-color: #0969da;
	}
	
	.align-icon {
		font-size: 14px;
		font-weight: bold;
	}
	
	/* 文字样式控制 */
	.text-style-controls {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 10px;
		gap: 8px;
	}
	
	.style-btn {
		width: 30px;
		height: 30px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f6f8fa;
		cursor: pointer;
	}
	
	.style-btn.active {
		background-color: #0969da;
		color: white;
		border-color: #0969da;
	}
	
	.style-icon {
		font-size: 14px;
		font-weight: bold;
	}
	
	.style-icon.italic {
		font-style: italic;
	}
	
	.style-icon.underline {
		text-decoration: underline;
	}
	
	.style-icon.strikethrough {
		text-decoration: line-through;
	}
	
	/* 字号控制 */
	.slider-control {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 10px;
		gap: 8px;
		min-height: 36px; /* 确保有足够的触摸区域 */
	}
	
	.slider-btn {
		width: 30px;
		height: 30px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f6f8fa;
		cursor: pointer;
		z-index: 2; /* 确保按钮在最上层 */
	}
	
	.slider-value {
		width: 40px;
		text-align: center;
		font-size: 14px;
		margin-left: auto;
		margin-right: 10px;
	}
	
	.property-slider {
		flex: 1;
		margin: 0 5px;
		z-index: 1;
	}
	
	/* 底部控制区域 */
	.bottom-controls {
		margin-top: 30px;
		padding-top: 20px;
		border-top: 1px solid #e0e0e0;
	}
	
	.control-group {
		margin-bottom: 20px;
	}
	
	.group-title {
		font-size: 14px;
		font-weight: bold;
		color: #24292f;
		margin-bottom: 10px;
		display: block;
	}
	
	.button-row {
		display: flex;
		gap: 10px;
		flex-wrap: wrap;
	}
	
	.control-btn {
		padding: 8px 16px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		background-color: #f6f8fa;
		font-size: 14px;
		color: #24292f;
		cursor: pointer;
		transition: all 0.2s;
		flex: 1;
		min-width: 80px;
		text-align: center;
	}
	
	.control-btn:hover {
		background-color: #f3f4f6;
		border-color: #8c959f;
	}
	
	.control-btn:active {
		background-color: #e5e7ea;
	}
	
	/* 颜色选择器弹窗 */
	.color-picker-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}
	
	.color-picker-content {
		background-color: white;
		border-radius: 8px;
		padding: 20px;
		max-width: 300px;
		width: 90%;
	}
	
	.color-picker-title {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20px;
		color: #24292f;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.color-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 15px;
		justify-content: center;
	}
	
	.color-item-container {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.color-item {
		width: 40px;
		height: 40px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		cursor: pointer;
		transition: transform 0.2s;
	}
	
	.color-item:hover {
		transform: scale(1.1);
	}
	
	.color-item-name {
		margin-top: 5px;
		font-size: 12px;
		color: #666;
		text-align: center;
	}
	
	/* 保存按钮样式 */
	.action-buttons {
		display: flex;
		justify-content: space-between;
		width: 90%;
		margin: 30px auto 40px auto;
	}
	
	.save-button {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		background-color: #8B0000; /* 深红色 */
		color: white;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		margin-left: 10px;
	}
	
	/* 预览按钮样式 */
	.preview-button {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		background-color: #007bff; /* 浅蓝色 */
		color: white;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		margin-right: 10px;
	}
	
	/* 批量操作选项 */
	.batch-options {
		margin: 10px 0 20px 0;
		padding: 10px 15px;
		background-color: #f6f8fa;
		border-radius: 8px;
		border: 1px solid #d0d7de;
	}
	
	.batch-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 0;
		font-size: 14px;
		color: #0969da;
		cursor: pointer;
	}
	
	.batch-option-text {
		font-weight: 500;
	}
	
	.batch-option-arrow {
		font-size: 12px;
		margin-left: 5px;
	}
	
	.special-btn {
		background-color: #8B0000; /* 深红色 */
		color: white;
		border-color: #8B0000;
		font-weight: bold;
	}
	
	.special-btn:hover {
		background-color: #a52a2a; /* 红褐色 */
		border-color: #a52a2a;
	}
	
	/* 预览弹窗样式 */
	.preview-popup-content {
		width: 90vw;
		height: 80vh;
		padding: 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		background-color: #ffffff;
		border-radius: 16rpx;
	}
	
	.preview-image-container {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 8rpx;
		margin: 20rpx 0;
		padding: 10rpx;
		overflow: hidden;
	}
	
	.preview-image {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	}
	
	.preview-loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
	}
	
	.preview-loading:before {
		content: "";
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 20rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #3498db;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.preview-error {
		font-size: 28rpx;
		color: #ff4d4f;
		text-align: center;
		margin-top: 20rpx;
	}
	
	.popup-btns {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
	}
	
	.popup-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.popup-btn.cancel {
		background-color: #f6f8fa;
		border: 1rpx solid #d0d7de;
		color: #24292f;
		margin-right: 20rpx;
	}
	
	.popup-btn.confirm {
		background-color: #0969da;
		color: white;
		margin-left: 20rpx;
	}
	
	/* 模板类型选择样式 */
	.template-type-section {
		display: flex;
		align-items: center;
		padding: 10px 5px;
		margin-bottom: 15px;
		background-color: #f6f8fa;
		border-radius: 8px;
	}
	
	.template-type-label {
		width: 80px;
		font-size: 14px;
		color: #24292f;
		flex-shrink: 0;
		font-weight: 500;
	}
	
	.template-type-picker {
		flex: 1;
	}
	
	.color-name {
		margin-left: 10px;
		flex: 1;
		font-size: 14px;
		color: #666;
	}
	
	/* 模板文字弹窗样式 */
	.template-popup-content {
		width: 100%;
		height: 66vh; /* 屏幕高度的2/3 */
		padding: 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
	}
	
	.popup-title {
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 40rpx;
		color: #24292f;
		padding-top: 20rpx;
	}
	
	.template-field {
		margin-bottom: 30rpx;
	}
	
	.field-label {
		display: block;
		font-size: 28rpx;
		color: #24292f;
		margin-bottom: 10rpx;
		font-weight: 500;
	}
	
	.field-input {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #d0d7de;
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		background-color: #f6f8fa;
	}
	
	.popup-btns {
		display: flex;
		justify-content: space-between;
		margin-top: auto;
		padding-top: 40rpx;
		padding-bottom: 30rpx;
	}
	
	.popup-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.popup-btn.cancel {
		background-color: #f6f8fa;
		border: 1rpx solid #d0d7de;
		color: #24292f;
		margin-right: 20rpx;
	}
	
	.popup-btn.confirm {
		background-color: #0969da;
		color: white;
		margin-left: 20rpx;
	}
</style>

