<template>
	<view class="canvas-container" id="canvasContainer" 
		@touchstart="onContainerTouchStart"
		@touchmove="onContainerTouchMove"
		@touchend="onContainerTouchEnd">
		<canvas 
			canvas-id="editCanvas" 
			id="editCanvas"
			class="edit-canvas" 
			:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
		></canvas>
	</view>
</template>

<script>
/**
 * Canvas编辑器组件
 * 负责Canvas绘制、元素操作、触摸交互等功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-06-24
 */
export default {
	name: 'CanvasEditor',
	props: {
		/** Canvas宽度 */
		canvasWidth: {
			type: Number,
			default: 300,
			validator: (value) => value > 0
		},
		/** Canvas高度 */
		canvasHeight: {
			type: Number,
			default: 180,
			validator: (value) => value > 0
		},
		/** Canvas上的元素数组 */
		canvasElements: {
			type: Array,
			default: () => [],
			validator: (value) => Array.isArray(value)
		},
		/** 当前选中的元素 */
		selectedElement: {
			type: Object,
			default: null
		},
		/** 背景图片路径 */
		backgroundImage: {
			type: String,
			default: '',
			validator: (value) => typeof value === 'string'
		},
		/** 背景颜色 */
		backgroundColor: {
			type: String,
			default: '#FFFFFF',
			validator: (value) => /^#[0-9A-Fa-f]{6}$/.test(value)
		}
	},
	emits: [
		'element-selected',
		'element-updated',
		'element-deleted',
		'element-edit'
	],
	data() {
		return {
			// Canvas相关
			/** @type {CanvasContext|null} Canvas绘制上下文 */
			canvasContext: null,
			/** @type {Object|null} Canvas容器的位置信息 */
			canvasRect: null,

			// 触摸交互相关
			/** @type {number} 触摸开始时的X坐标 */
			touchStartX: 0,
			/** @type {number} 触摸开始时的Y坐标 */
			touchStartY: 0,
			/** @type {boolean} 是否正在拖拽 */
			isDragging: false,
			/** @type {boolean} 是否正在缩放 */
			isScaling: false,
			/** @type {Array|null} 上次触摸点数组 */
			lastTouches: null,
			/** @type {number} 初始两指距离 */
			initialDistance: 0,
			/** @type {number} 上次缩放因子 */
			lastScaleFactor: 1,

			// 操作图标位置
			/** @type {Object|null} 编辑图标位置信息 */
			editIconPosition: null,
			/** @type {Object|null} 删除图标位置信息 */
			deleteIconPosition: null,
			/** @type {Object|null} 旋转图标位置信息 */
			rotateIconPosition: null,

			// 背景图片信息缓存
			/** @type {Object|null} 背景图片信息 */
			backgroundImageInfo: null,

			// 性能优化相关
			/** @type {number|null} 绘制防抖定时器 */
			drawTimer: null,
			/** @type {number} 上次绘制时间戳 */
			lastDrawTime: 0,
			/** @type {Map} 文本测量结果缓存 */
			textMetricsCache: new Map(),
			/** @type {boolean} 是否正在绘制中 */
			isDrawing: false
		}
	},
	mounted() {
		this.initCanvas();
	},
	beforeDestroy() {
		this.cleanup();
	},
	watch: {
		canvasElements: {
			handler() {
				this.debouncedDraw();
			},
			deep: true
		},
		selectedElement() {
			this.debouncedDraw();
		},
		backgroundImage() {
			this.backgroundImageInfo = null; // 清除缓存
			this.debouncedDraw();
		},
		backgroundColor() {
			this.debouncedDraw();
		}
	},
	methods: {
		/**
		 * 初始化Canvas
		 * 创建Canvas上下文并开始首次绘制
		 * @returns {void}
		 */
		initCanvas() {
			try {
				setTimeout(() => {
					this.canvasContext = uni.createCanvasContext('editCanvas', this);
					if (!this.canvasContext) {
						console.error('Canvas上下文创建失败');
						return;
					}
					this.updateCanvasRect();
					this.drawCanvas();
				}, 200);
			} catch (error) {
				console.error('初始化Canvas失败:', error);
				this.$emit('error', { type: 'CANVAS_INIT_ERROR', error });
			}
		},

		/**
		 * 获取Canvas容器的位置信息
		 * 用于触摸事件坐标转换
		 * @returns {void}
		 */
		updateCanvasRect() {
			try {
				const query = uni.createSelectorQuery().in(this);
				query.select('#canvasContainer').boundingClientRect(data => {
					if (data) {
						this.canvasRect = data;
					} else {
						console.warn('无法获取Canvas容器位置信息');
					}
				}).exec();
			} catch (error) {
				console.error('获取Canvas容器位置失败:', error);
			}
		},

		/**
		 * 防抖绘制
		 * 避免频繁重绘，提高性能
		 * @returns {void}
		 */
		debouncedDraw() {
			if (this.drawTimer) {
				clearTimeout(this.drawTimer);
			}

			this.drawTimer = setTimeout(() => {
				this.drawCanvas();
			}, 16); // 约60fps
		},

		/**
		 * 绘制Canvas
		 * 主要绘制方法，包含性能优化和错误处理
		 * @returns {void}
		 */
		drawCanvas() {
			// 基础检查
			if (!this.canvasContext || this.isDrawing) {
				return;
			}

			// 防止重复绘制，限制最高60fps
			const now = Date.now();
			if (now - this.lastDrawTime < 16) {
				return;
			}

			try {
				this.isDrawing = true;
				this.lastDrawTime = now;

				const ctx = this.canvasContext;

				// 清空画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制背景
				this.drawBackground(ctx);

				// 绘制所有元素
				this.canvasElements.forEach((element, index) => {
					try {
						this.drawElement(element);
					} catch (error) {
						console.error(`绘制元素${index}失败:`, error, element);
					}
				});

				// 绘制选中状态
				if (this.selectedElement) {
					try {
						this.drawSelectionHandles(this.selectedElement);
					} catch (error) {
						console.error('绘制选中状态失败:', error);
					}
				}

				// 提交绘制
				ctx.draw(false, () => {
					this.isDrawing = false;
				});

			} catch (error) {
				console.error('Canvas绘制失败:', error);
				this.isDrawing = false;
				this.$emit('error', { type: 'CANVAS_DRAW_ERROR', error });
			}
		},
		
		/**
		 * 绘制背景
		 * 支持背景图片和纯色背景
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @returns {void}
		 */
		drawBackground(ctx) {
			try {
				if (this.backgroundImage) {
					if (this.backgroundImageInfo) {
						// 使用缓存的图片信息绘制
						this.drawBackgroundWithRatio(ctx, this.backgroundImage, this.backgroundImageInfo);
					} else {
						// 异步获取图片信息
						uni.getImageInfo({
							src: this.backgroundImage,
							success: (imageInfo) => {
								if (imageInfo && imageInfo.width && imageInfo.height) {
									this.backgroundImageInfo = imageInfo;
									this.drawBackgroundWithRatio(ctx, this.backgroundImage, imageInfo);
									this.debouncedDraw();
								} else {
									console.warn('获取到的图片信息不完整:', imageInfo);
									this.drawSolidBackground(ctx);
								}
							},
							fail: (error) => {
								console.error('获取背景图片信息失败:', error);
								this.drawSolidBackground(ctx);
							}
						});
						// 先绘制纯色背景作为占位
						this.drawSolidBackground(ctx);
						return;
					}
				} else {
					// 绘制纯色背景
					this.drawSolidBackground(ctx);
				}
			} catch (error) {
				console.error('绘制背景失败:', error);
				// 降级到纯色背景
				this.drawSolidBackground(ctx);
			}
		},

		/**
		 * 绘制纯色背景
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @returns {void}
		 */
		drawSolidBackground(ctx) {
			try {
				ctx.setFillStyle(this.backgroundColor);
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
			} catch (error) {
				console.error('绘制纯色背景失败:', error);
			}
		},
		
		/**
		 * 按比例绘制背景图片
		 * 保持图片宽高比，居中显示
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {string} imagePath - 图片路径
		 * @param {Object} imageInfo - 图片信息对象
		 * @returns {void}
		 */
		drawBackgroundWithRatio(ctx, imagePath, imageInfo) {
			try {
				// 参数验证
				if (!imageInfo || !imageInfo.width || !imageInfo.height) {
					console.error('图片信息不完整:', imageInfo);
					this.drawSolidBackground(ctx);
					return;
				}

				const imageRatio = imageInfo.width / imageInfo.height;
				const canvasRatio = this.canvasWidth / this.canvasHeight;

				let drawWidth, drawHeight, offsetX = 0, offsetY = 0;

				// 计算绘制尺寸和偏移量
				if (imageRatio > canvasRatio) {
					// 图片更宽，以高度为基准
					drawHeight = this.canvasHeight;
					drawWidth = drawHeight * imageRatio;
					offsetX = (this.canvasWidth - drawWidth) / 2;
				} else {
					// 图片更高，以宽度为基准
					drawWidth = this.canvasWidth;
					drawHeight = drawWidth / imageRatio;
					offsetY = (this.canvasHeight - drawHeight) / 2;
				}

				// 先填充背景色
				ctx.setFillStyle(this.backgroundColor);
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制图片
				ctx.drawImage(imagePath, offsetX, offsetY, drawWidth, drawHeight);

			} catch (error) {
				console.error('按比例绘制背景图片失败:', error);
				// 降级到纯色背景
				this.drawSolidBackground(ctx);
			}
		},
		
		/**
		 * 绘制单个元素
		 * 根据元素类型调用相应的绘制方法
		 * @param {Object} element - 要绘制的元素对象
		 * @returns {void}
		 */
		drawElement(element) {
			// 参数验证
			if (!element || !element.type) {
				console.warn('元素对象无效:', element);
				return;
			}

			const ctx = this.canvasContext;
			if (!ctx) {
				console.error('Canvas上下文不存在');
				return;
			}

			ctx.save();

			try {
				switch (element.type) {
					case 'text':
						this.drawTextElement(ctx, element);
						break;
					case 'image':
						this.drawImageElement(ctx, element);
						break;
					default:
						console.warn('未知的元素类型:', element.type);
				}
			} catch (error) {
				console.error(`绘制${element.type}元素失败:`, error, element);
			} finally {
				ctx.restore();
			}
		},
		
		/**
		 * 绘制文本元素
		 * 支持字体样式、颜色、对齐方式、高度拉伸等
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} element - 文本元素对象
		 * @returns {void}
		 */
		drawTextElement(ctx, element) {
			try {
				// 参数验证
				if (!element.text || typeof element.text !== 'string') {
					console.warn('文本内容无效:', element.text);
					return;
				}

				// 设置文本颜色
				const color = element.color || '#000000';
				if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
					console.warn('文本颜色格式无效:', color);
				}
				ctx.setFillStyle(color);

				// 设置字体样式
				const fontStyle = element.isItalic ? 'italic' : 'normal';
				const fontWeight = element.isBold ? 'bold' : 'normal';
				const fontSize = Math.max(8, Math.min(80, element.fontSize || 16)); // 限制字号范围
				const fontFamily = element.fontFamily || 'sans-serif';

				try {
					ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
				} catch (error) {
					console.warn('设置字体失败，使用默认字体:', error);
					ctx.font = `${fontSize}px sans-serif`;
				}

				// 设置文本对齐
				const textAlign = ['left', 'center', 'right'].includes(element.textAlign)
					? element.textAlign : 'left';
				ctx.setTextAlign(textAlign);

				// 处理高度拉伸
				const heightScale = element.heightScale || 1;
				if (heightScale !== 1 && heightScale > 0) {
					ctx.scale(1, heightScale);
					const scaledY = element.y / heightScale;
					ctx.fillText(element.text, element.x, scaledY);
				} else {
					ctx.fillText(element.text, element.x, element.y);
				}

				// 绘制文本装饰线
				if (element.isUnderline || element.isStrikethrough) {
					this.drawTextDecorations(ctx, element);
				}

			} catch (error) {
				console.error('绘制文本元素失败:', error, element);
			}
		},
		
		/**
		 * 绘制图片元素
		 * 支持位置约束、旋转等功能
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} element - 图片元素对象
		 * @returns {void}
		 */
		drawImageElement(ctx, element) {
			try {
				// 参数验证
				if (!element.src) {
					console.warn('图片源路径为空:', element);
					return;
				}

				if (!element.width || !element.height || element.width <= 0 || element.height <= 0) {
					console.warn('图片尺寸无效:', element.width, element.height);
					return;
				}

				// 边界约束
				let x = Math.max(0, Math.min(element.x || 0, this.canvasWidth - element.width));
				let y = Math.max(0, Math.min(element.y || 0, this.canvasHeight - element.height));

				// 更新元素位置（如果发生了约束）
				if (x !== element.x || y !== element.y) {
					element.x = x;
					element.y = y;
				}

				// 处理旋转
				const rotation = element.rotation || 0;
				if (rotation !== 0) {
					const centerX = x + element.width / 2;
					const centerY = y + element.height / 2;

					// 验证旋转角度
					const normalizedRotation = rotation % 360;

					ctx.translate(centerX, centerY);
					ctx.rotate(normalizedRotation * Math.PI / 180);
					ctx.drawImage(element.src, -element.width / 2, -element.height / 2, element.width, element.height);
				} else {
					// 无旋转时直接绘制
					ctx.drawImage(element.src, x, y, element.width, element.height);
				}

			} catch (error) {
				console.error('绘制图片元素失败:', error, element);
				// 可以在这里绘制一个错误占位符
				this.drawErrorPlaceholder(ctx, element);
			}
		},

		/**
		 * 绘制错误占位符
		 * 当图片加载失败时显示
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} element - 元素对象
		 * @returns {void}
		 */
		drawErrorPlaceholder(ctx, element) {
			try {
				const x = element.x || 0;
				const y = element.y || 0;
				const width = element.width || 50;
				const height = element.height || 50;

				// 绘制错误占位框
				ctx.setStrokeStyle('#ff0000');
				ctx.setLineWidth(2);
				ctx.strokeRect(x, y, width, height);

				// 绘制X标记
				ctx.beginPath();
				ctx.moveTo(x, y);
				ctx.lineTo(x + width, y + height);
				ctx.moveTo(x + width, y);
				ctx.lineTo(x, y + height);
				ctx.stroke();

			} catch (error) {
				console.error('绘制错误占位符失败:', error);
			}
		},
		
		// 绘制文本装饰线
		drawTextDecorations(ctx, element) {
			const metrics = ctx.measureText(element.text);
			const textWidth = metrics.width;
			let lineY = element.y;
			
			if (element.heightScale && element.heightScale !== 1) {
				lineY = element.y / element.heightScale;
			}
			
			if (element.isUnderline) {
				lineY = lineY + 2;
			}
			if (element.isStrikethrough) {
				lineY = lineY - (element.fontSize || 16) / 3;
			}
			
			let lineStartX = element.x;
			if (element.textAlign === 'center') {
				lineStartX = element.x - textWidth / 2;
			} else if (element.textAlign === 'right') {
				lineStartX = element.x - textWidth;
			}
			
			ctx.beginPath();
			ctx.setStrokeStyle(element.color || '#000000');
			ctx.setLineWidth(1);
			ctx.moveTo(lineStartX, lineY);
			ctx.lineTo(lineStartX + textWidth, lineY);
			ctx.stroke();
		},
		
		// 绘制选中状态
		drawSelectionHandles(element) {
			const ctx = this.canvasContext;
			const box = this.getElementBoundingBox(element);
			
			ctx.save();
			ctx.setStrokeStyle('#0969da');
			ctx.setLineWidth(2);
			
			if (element.type === 'image' && element.rotation && element.rotation !== 0) {
				const centerX = element.x + element.width / 2;
				const centerY = element.y + element.height / 2;
				
				ctx.translate(centerX, centerY);
				ctx.rotate(element.rotation * Math.PI / 180);
				
				const halfWidth = element.width / 2;
				const halfHeight = element.height / 2;
				ctx.strokeRect(-halfWidth, -halfHeight, element.width, element.height);
				
				ctx.rotate(-element.rotation * Math.PI / 180);
				ctx.translate(-centerX, -centerY);
			} else {
				ctx.strokeRect(box.x, box.y, box.width, box.height);
			}
			
			// 绘制操作图标
			this.drawOperationIcons(ctx, element, box);
			
			ctx.restore();
		},
		
		// 绘制操作图标
		drawOperationIcons(ctx, element, box) {
			const iconRadius = 18;
			
			if (element.type === 'image') {
				// 图片元素显示删除和旋转图标
				const deleteIconX = box.x + box.width + iconRadius;
				const deleteIconY = box.y - iconRadius;
				const rotateIconX = box.x - iconRadius;
				const rotateIconY = box.y - iconRadius;
				
				this.deleteIconPosition = { x: deleteIconX, y: deleteIconY, radius: iconRadius * 1.5 };
				this.rotateIconPosition = { x: rotateIconX, y: rotateIconY, radius: iconRadius * 1.5 };
				this.editIconPosition = null;
				
				// 绘制删除图标
				ctx.beginPath();
				ctx.setFillStyle('#f44336');
				ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制旋转图标
				ctx.beginPath();
				ctx.setFillStyle('#0969da');
				ctx.arc(rotateIconX, rotateIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制图标内容
				this.drawDeleteIcon(ctx, deleteIconX, deleteIconY);
				this.drawRotateIcon(ctx, rotateIconX, rotateIconY, iconRadius);
				
			} else {
				// 文本元素显示编辑和删除图标
				const editIconX = box.x - iconRadius;
				const editIconY = box.y - iconRadius;
				const deleteIconX = box.x + box.width + iconRadius;
				const deleteIconY = box.y - iconRadius;
				
				this.editIconPosition = { x: editIconX, y: editIconY, radius: iconRadius * 1.5 };
				this.deleteIconPosition = { x: deleteIconX, y: deleteIconY, radius: iconRadius * 1.5 };
				this.rotateIconPosition = null;
				
				// 绘制编辑图标
				ctx.beginPath();
				ctx.setFillStyle('#0969da');
				ctx.arc(editIconX, editIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制删除图标
				ctx.beginPath();
				ctx.setFillStyle('#f44336');
				ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制图标内容
				this.drawEditIcon(ctx, editIconX, editIconY);
				this.drawDeleteIcon(ctx, deleteIconX, deleteIconY);
			}
		},

		// 绘制删除图标
		drawDeleteIcon(ctx, x, y) {
			ctx.beginPath();
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2.5);
			ctx.moveTo(x - 7, y - 7);
			ctx.lineTo(x + 7, y + 7);
			ctx.moveTo(x + 7, y - 7);
			ctx.lineTo(x - 7, y + 7);
			ctx.stroke();
		},

		// 绘制编辑图标
		drawEditIcon(ctx, x, y) {
			ctx.setFillStyle('#ffffff');
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2);
			ctx.beginPath();
			ctx.moveTo(x - 7, y + 7);
			ctx.lineTo(x + 5, y - 5);
			ctx.lineTo(x + 7, y - 3);
			ctx.lineTo(x - 5, y + 9);
			ctx.closePath();
			ctx.fill();
		},

		// 绘制旋转图标
		drawRotateIcon(ctx, x, y, radius) {
			ctx.beginPath();
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2);
			ctx.arc(x, y, radius * 0.6, 0.3 * Math.PI, 2.2 * Math.PI);
			ctx.stroke();

			ctx.beginPath();
			ctx.moveTo(x + 6, y - 6);
			ctx.lineTo(x, y - 10);
			ctx.lineTo(x - 4, y - 6);
			ctx.setFillStyle('#ffffff');
			ctx.fill();
		},

		// 获取文本宽度（带缓存）
		getTextWidth(element) {
			const cacheKey = `${element.text}_${element.fontSize}_${element.fontFamily}_${element.isBold}_${element.isItalic}`;

			if (this.textMetricsCache.has(cacheKey)) {
				return this.textMetricsCache.get(cacheKey);
			}

			const ctx = this.canvasContext;
			const fontStyle = element.isItalic ? 'italic' : 'normal';
			const fontWeight = element.isBold ? 'bold' : 'normal';
			const fontSize = element.fontSize || 16;
			const fontFamily = element.fontFamily || 'sans-serif';

			ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;

			let width = 0;
			try {
				const metrics = ctx.measureText(element.text);
				width = metrics.width;
			} catch (e) {
				width = element.text.length * fontSize * 0.6;
			}

			// 缓存结果，限制缓存大小
			if (this.textMetricsCache.size > 100) {
				const firstKey = this.textMetricsCache.keys().next().value;
				this.textMetricsCache.delete(firstKey);
			}
			this.textMetricsCache.set(cacheKey, width);

			return width;
		},

		// 获取元素边界框
		getElementBoundingBox(element) {
			if (element.type === 'image') {
				return {
					x: element.x,
					y: element.y,
					width: element.width,
					height: element.height
				};
			} else if (element.type === 'text') {
				const fontSize = element.fontSize || 16;
				const width = this.getTextWidth(element);

				let x = element.x;
				if (element.textAlign === 'center') {
					x = element.x - width / 2;
				} else if (element.textAlign === 'right') {
					x = element.x - width;
				}

				return {
					x: x,
					y: element.y - fontSize,
					width: width,
					height: fontSize * 1.5
				};
			}

			return {
				x: element.x,
				y: element.y,
				width: 10,
				height: 10
			};
		},

		// 触摸事件处理
		onContainerTouchStart(e) {
			e.preventDefault && e.preventDefault();

			if (e.touches.length === 2 && this.selectedElement && this.selectedElement.type === 'image') {
				this.isScaling = true;
				this.isDragging = false;

				const touch1 = e.touches[0];
				const touch2 = e.touches[1];
				const dx = touch1.clientX - touch2.clientX;
				const dy = touch1.clientY - touch2.clientY;
				this.initialDistance = Math.sqrt(dx * dx + dy * dy);

				this.lastTouches = [...e.touches];
				return;
			}

			const touch = e.touches[0];

			if (!this.canvasRect) {
				this.updateCanvasRect();
				return;
			}

			const canvasX = touch.clientX - this.canvasRect.left;
			const canvasY = touch.clientY - this.canvasRect.top;

			this.touchStartX = canvasX;
			this.touchStartY = canvasY;

			// 检查是否点击了操作图标
			if (this.selectedElement) {
				if (this.deleteIconPosition && this.isPointInCircle(canvasX, canvasY, this.deleteIconPosition)) {
					setTimeout(() => {
						this.$emit('element-deleted', this.selectedElement);
					}, 100);
					return;
				}

				if (this.editIconPosition && this.isPointInCircle(canvasX, canvasY, this.editIconPosition)) {
					setTimeout(() => {
						this.$emit('element-edit', this.selectedElement);
					}, 100);
					return;
				}

				if (this.rotateIconPosition && this.isPointInCircle(canvasX, canvasY, this.rotateIconPosition)) {
					setTimeout(() => {
						this.rotateSelectedElement();
					}, 100);
					return;
				}
			}

			// 选择元素
			const selectedElement = this.selectElementAt(canvasX, canvasY);
			this.$emit('element-selected', selectedElement);

			if (selectedElement) {
				this.isDragging = true;
			}
		},

		onContainerTouchMove(e) {
			if (e.touches.length === 2 && this.isScaling && this.selectedElement && this.selectedElement.type === 'image') {
				const touch1 = e.touches[0];
				const touch2 = e.touches[1];
				const dx = touch1.clientX - touch2.clientX;
				const dy = touch1.clientY - touch2.clientY;
				const currentDistance = Math.sqrt(dx * dx + dy * dy);

				const scaleFactor = currentDistance / this.initialDistance;

				if (scaleFactor > 0.1) {
					const originalWidth = this.selectedElement.width / (this.lastScaleFactor || 1);
					const originalHeight = this.selectedElement.height / (this.lastScaleFactor || 1);

					let newWidth = originalWidth * scaleFactor;
					let newHeight = originalHeight * scaleFactor;

					const minSize = 30;
					if (newWidth < minSize) {
						newWidth = minSize;
						newHeight = (minSize / originalWidth) * originalHeight;
					}
					if (newHeight < minSize) {
						newHeight = minSize;
						newWidth = (minSize / originalHeight) * originalWidth;
					}

					const maxWidth = this.canvasWidth * 0.95;
					const maxHeight = this.canvasHeight * 0.95;
					if (newWidth > maxWidth) {
						newWidth = maxWidth;
						newHeight = (maxWidth / originalWidth) * originalHeight;
					}
					if (newHeight > maxHeight) {
						newHeight = maxHeight;
						newWidth = (maxHeight / originalHeight) * originalWidth;
					}

					this.selectedElement.width = newWidth;
					this.selectedElement.height = newHeight;
					this.lastScaleFactor = scaleFactor;

					let newX = this.selectedElement.x;
					let newY = this.selectedElement.y;

					if (newX < 0) newX = 0;
					if (newY < 0) newY = 0;
					if (newX + newWidth > this.canvasWidth) newX = this.canvasWidth - newWidth;
					if (newY + newHeight > this.canvasHeight) newY = this.canvasHeight - newHeight;

					this.selectedElement.x = newX;
					this.selectedElement.y = newY;

					this.$emit('element-updated', this.selectedElement);
					this.debouncedDraw();
				}

				this.lastTouches = [...e.touches];
				return;
			}

			if (!this.isDragging || !this.selectedElement || !this.canvasRect) return;

			e.preventDefault && e.preventDefault();

			const touch = e.touches[0];
			const canvasX = touch.clientX - this.canvasRect.left;
			const canvasY = touch.clientY - this.canvasRect.top;

			const deltaX = canvasX - this.touchStartX;
			const deltaY = canvasY - this.touchStartY;

			let newX = this.selectedElement.x + deltaX;
			let newY = this.selectedElement.y + deltaY;

			// 边界检查
			const box = this.getElementBoundingBox(this.selectedElement);

			if (newX < 0) newX = 0;
			if (this.selectedElement.type === 'text') {
				if (newY < box.height) newY = box.height;
			} else {
				if (newY < 0) newY = 0;
			}

			if (newX + box.width > this.canvasWidth) {
				newX = this.canvasWidth - box.width;
			}
			if (newY + box.height > this.canvasHeight) {
				newY = this.canvasHeight - box.height;
			}

			this.selectedElement.x = newX;
			this.selectedElement.y = newY;

			this.touchStartX = canvasX;
			this.touchStartY = canvasY;

			this.$emit('element-updated', this.selectedElement);
			this.debouncedDraw();
		},

		onContainerTouchEnd() {
			this.isDragging = false;
			this.isScaling = false;
			this.lastScaleFactor = 1;
			this.lastTouches = null;
		},

		// 判断点是否在圆内
		isPointInCircle(x, y, circle) {
			const dx = x - circle.x;
			const dy = y - circle.y;
			const distance = Math.sqrt(dx * dx + dy * dy);
			return distance <= circle.radius;
		},

		// 旋转选中的元素
		rotateSelectedElement() {
			if (!this.selectedElement || this.selectedElement.type !== 'image') return;

			if (this.selectedElement.rotation === undefined) {
				this.selectedElement.rotation = 0;
			}

			this.selectedElement.rotation = (this.selectedElement.rotation + 45) % 360;

			this.$emit('element-updated', this.selectedElement);
			this.debouncedDraw();

			uni.showToast({
				title: `已旋转至${this.selectedElement.rotation}°`,
				icon: 'none',
				duration: 1500
			});
		},

		// 选择元素
		selectElementAt(x, y) {
			for (let i = this.canvasElements.length - 1; i >= 0; i--) {
				const element = this.canvasElements[i];
				const box = this.getElementBoundingBox(element);

				if (x >= box.x && x <= box.x + box.width && y >= box.y && y <= box.y + box.height) {
					return element;
				}
			}
			return null;
		},

		// 清理资源
		cleanup() {
			if (this.drawTimer) {
				clearTimeout(this.drawTimer);
				this.drawTimer = null;
			}
			this.textMetricsCache.clear();
			this.canvasContext = null;
			this.canvasRect = null;
		},

		// 公共方法
		exportCanvas() {
			return new Promise((resolve, reject) => {
				uni.canvasToTempFilePath({
					canvasId: 'editCanvas',
					success: resolve,
					fail: reject
				}, this);
			});
		}
	}
}
</script>

<style scoped>
.canvas-container {
	padding: 10px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #ffffff;
	flex: 0 0 33.33vh;
}

.edit-canvas {
	background-color: #f5f5f5;
	border: 1px solid #e0e0e0;
}
</style>
